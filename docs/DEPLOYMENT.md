# 🚀 Deployment Guide

This guide covers deploying Portfolio Builder to production environments, with a focus on Vercel deployment and proper configuration of all services.

## 🎯 Deployment Overview

Portfolio Builder is designed as a Jamstack application optimized for deployment on **Vercel**, with the following external services:
- **Firebase**: Authentication and database
- **Cloudinary**: Image storage and optimization
- **Vercel**: Hosting and edge functions

## 🔧 Pre-deployment Setup

### 1. Firebase Project Setup

#### Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enable Google Analytics (optional)
4. Wait for project creation

#### Enable Authentication
1. Go to Authentication → Sign-in method
2. Enable Google provider
3. Add your domain to authorized domains
4. Note down the configuration values

#### Setup Firestore Database
1. Go to Firestore Database
2. Create database in production mode
3. Set up security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own portfolio
    match /portfolios/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Published portfolios are publicly readable
    match /portfolios/{userId} {
      allow read: if resource.data.isPublished == true;
    }
  }
}
```

#### Generate Service Account Key
1. Go to Project Settings → Service accounts
2. Generate new private key
3. Download JSON file (keep secure!)

### 2. Cloudinary Setup

#### Create Cloudinary Account
1. Sign up at [Cloudinary](https://cloudinary.com/)
2. Go to Dashboard
3. Note down Cloud Name, API Key, API Secret

#### Create Upload Preset
1. Go to Settings → Upload
2. Add upload preset:
   - Name: `portfolio-uploads`
   - Signing Mode: `Unsigned`
   - Folder: `portfolio-builder`
   - Transformations: Auto-optimize quality and format

### 3. Domain Configuration

#### Custom Domain (Optional)
1. Purchase domain from registrar
2. Configure DNS to point to Vercel
3. Add domain in Vercel dashboard

## 🌐 Vercel Deployment

### 1. Repository Setup

#### Connect Repository
1. Push code to GitHub/GitLab/Bitbucket
2. Go to [Vercel Dashboard](https://vercel.com/dashboard)
3. Click "New Project"
4. Import your repository

### 2. Environment Variables

Configure the following environment variables in Vercel:

#### Firebase Configuration
```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

#### Firebase Admin (Server-side)
```env
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
```

#### Cloudinary Configuration
```env
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=portfolio-uploads
```

### 3. Build Configuration

#### Vercel Configuration (`vercel.json`)
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "rewrites": [
    {
      "source": "/api/:path*",
      "destination": "/api/:path*"
    }
  ]
}
```

#### Build Script Updates
Ensure your `package.json` includes theme compilation:

```json
{
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "npm run sync-themes && next build",
    "start": "next start",
    "lint": "next lint",
    "sync-themes": "node scripts/sync-themes.js"
  }
}
```

### 4. Deploy

1. **Automatic Deployment**: Push to main branch
2. **Manual Deployment**: Use Vercel CLI
   ```bash
   npm i -g vercel
   vercel --prod
   ```

## 🔒 Security Configuration

### 1. Firebase Security

#### Authentication Settings
- Enable only required sign-in methods
- Configure authorized domains
- Set up proper OAuth redirect URIs

#### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Portfolio access control
    match /portfolios/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if resource.data.isPublished == true;
    }
    
    // Prevent unauthorized access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

### 2. Environment Security

#### Secure Environment Variables
- Never commit `.env` files to version control
- Use Vercel's environment variable encryption
- Rotate keys regularly
- Use different keys for staging/production

#### CORS Configuration
```typescript
// next.config.ts
const nextConfig = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: 'https://yourdomain.com' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ];
  },
};
```

## 📊 Performance Optimization

### 1. Next.js Optimizations

#### Image Optimization
```typescript
// next.config.ts
const nextConfig = {
  images: {
    domains: ['res.cloudinary.com'],
    formats: ['image/webp', 'image/avif'],
  },
};
```

#### Bundle Analysis
```bash
npm install --save-dev @next/bundle-analyzer
```

### 2. Caching Strategy

#### Static Assets
- Automatic caching via Vercel CDN
- Long-term caching for theme CSS files
- Proper cache headers for images

#### API Responses
```typescript
// API route caching
export async function GET() {
  const response = NextResponse.json(data);
  response.headers.set('Cache-Control', 's-maxage=3600, stale-while-revalidate');
  return response;
}
```

## 📈 Monitoring & Analytics

### 1. Vercel Analytics

#### Enable Analytics
1. Go to Vercel project dashboard
2. Navigate to Analytics tab
3. Enable Web Analytics
4. Add analytics script to layout

#### Performance Monitoring
- Core Web Vitals tracking
- Function execution monitoring
- Error tracking and alerts

### 2. Firebase Monitoring

#### Performance Monitoring
```typescript
// Add to app/layout.tsx
import { getPerformance } from 'firebase/performance';

if (typeof window !== 'undefined') {
  const perf = getPerformance(app);
}
```

#### Error Tracking
```typescript
// Add error boundary
import { logEvent, getAnalytics } from 'firebase/analytics';

const analytics = getAnalytics(app);
logEvent(analytics, 'error', { error_message: error.message });
```

## 🔄 CI/CD Pipeline

### 1. GitHub Actions (Optional)

#### Build and Test Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Vercel
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Build project
        run: npm run build
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

### 2. Automated Testing

#### Pre-deployment Checks
```bash
# Add to package.json
{
  "scripts": {
    "test": "jest",
    "test:e2e": "playwright test",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  }
}
```

## 🐛 Troubleshooting

### Common Deployment Issues

#### Build Failures
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check TypeScript errors
npm run type-check
```

#### Environment Variable Issues
- Verify all required variables are set
- Check for typos in variable names
- Ensure proper escaping of special characters

#### Firebase Connection Issues
- Verify Firebase project configuration
- Check security rules
- Ensure service account has proper permissions

#### Theme CSS Issues
```bash
# Manually compile themes
npm run sync-themes

# Check theme compilation logs
node scripts/sync-themes.js --verbose
```

### Performance Issues

#### Slow Build Times
- Enable Vercel build cache
- Optimize dependencies
- Use Next.js bundle analyzer

#### Runtime Performance
- Monitor Core Web Vitals
- Optimize images with Cloudinary
- Use proper caching headers

## 📋 Post-deployment Checklist

### ✅ Functionality Testing
- [ ] User authentication works
- [ ] Portfolio creation and editing
- [ ] Theme switching
- [ ] Image uploads
- [ ] Portfolio publishing
- [ ] Static export functionality
- [ ] Mobile responsiveness

### ✅ Performance Testing
- [ ] Page load times < 3 seconds
- [ ] Core Web Vitals in green
- [ ] Image optimization working
- [ ] CDN caching effective

### ✅ Security Testing
- [ ] Authentication required for private routes
- [ ] Firestore security rules working
- [ ] No sensitive data exposed
- [ ] HTTPS enforced

### ✅ Monitoring Setup
- [ ] Vercel Analytics enabled
- [ ] Error tracking configured
- [ ] Performance monitoring active
- [ ] Uptime monitoring (optional)

## 🔄 Maintenance

### Regular Tasks
- **Weekly**: Check error logs and performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and rotate API keys
- **Annually**: Audit security configurations

### Backup Strategy
- **Firebase**: Automatic backups enabled
- **Code**: Version control with Git
- **Environment**: Document all configurations
- **Assets**: Cloudinary provides redundancy

---

Following this deployment guide ensures a robust, secure, and performant production deployment of Portfolio Builder. The architecture is designed to scale automatically with user growth while maintaining optimal performance.
