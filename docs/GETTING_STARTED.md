# 📖 Getting Started Guide

Welcome to Portfolio Builder! This guide will help you set up the project locally and understand the basic workflow.

## 🚀 Quick Start

### Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js** (v18 or higher)
- **npm** or **yarn**
- **Git**

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd portfolio-builder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   # Firebase Configuration
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

   # Cloudinary Configuration
   NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
   NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=your_upload_preset

   # Firebase Admin (for server-side operations)
   FIREBASE_PRIVATE_KEY=your_private_key
   FIREBASE_CLIENT_EMAIL=your_client_email
   ```

4. **Compile theme CSS files**
   ```bash
   npm run sync-themes
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 🎯 Basic Workflow

### For Users (Portfolio Creation)

1. **Sign In**: Use Google authentication to sign in
2. **Choose Template**: Select from available themes (Modern, Creative Minimalist)
3. **Edit Portfolio**: Use the inline editor to customize your content
4. **Preview**: Check how your portfolio looks on different devices
5. **Publish**: Make your portfolio live with a custom URL
6. **Export**: Download a static version of your portfolio

### For Developers (Theme Development)

1. **Create New Theme**
   ```bash
   node scripts/scaffold-theme.js my-awesome-theme
   ```

2. **Develop Components**
   - Edit React components in `themes/my-awesome-theme/components/`
   - Style with CSS in `themes/my-awesome-theme/components/*.css`

3. **Register Theme**
   Add your theme to `themes/theme-registry.ts`

4. **Compile and Test**
   ```bash
   npm run sync-themes
   npm run dev
   ```

## 📁 Project Structure

```
portfolio-builder/
├── app/                    # Next.js App Router
│   ├── (private)/         # Protected routes (dashboard, editor)
│   ├── (public)/          # Public routes (portfolios, preview)
│   └── api/               # API routes
├── components/            # Shared UI components
├── contexts/              # React contexts (auth, editor, export)
├── docs/                  # Documentation
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions and configurations
├── public/                # Static assets and compiled theme CSS
├── scripts/               # Build and development scripts
├── stores/                # State management (Zustand)
└── themes/                # Theme system
    ├── creative-minimalist/
    ├── modern/
    ├── shared/            # Shared theme utilities
    └── theme-registry.ts  # Theme registration
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run sync-themes` - Compile all theme CSS files
- `node scripts/scaffold-theme.js <name>` - Create new theme

## 🎨 Theme System Overview

Portfolio Builder uses a modular theme system:

- **Themes** are located in `themes/[theme-name]/`
- Each theme has **React components** and **CSS files**
- **Modular CSS architecture** with component-specific styles
- **Universal compiler** automatically detects and compiles all themes
- **Auto-sync** to public directory for serving

## 📤 Export System

The export system uses **Live DOM Capture** technology:

1. **Primary Method**: Captures the actual rendered DOM from live portfolio
2. **Fallback Method**: Template-based HTML generation
3. **Output**: ZIP file with `index.html`, `styles.css`, and `README.md`
4. **Features**: Pixel-perfect export, mobile menu functionality, optimized images

## 🔐 Authentication & Security

- **Google OAuth** for user authentication
- **Firebase Authentication** for secure user management
- **Firestore Security Rules** for data protection
- **Server-side validation** for sensitive operations

## 🌐 Deployment

The application is designed for deployment on **Vercel**:

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## 🆘 Troubleshooting

### Common Issues

**Theme CSS not loading**
```bash
npm run sync-themes
```

**Build errors**
```bash
rm -rf .next
npm run build
```

**Firebase connection issues**
- Check environment variables
- Verify Firebase project configuration
- Ensure Firestore security rules are properly set

**Export not working**
- Check browser console for errors
- Verify theme CSS is compiled
- Try refreshing the page before export

## 📞 Support

For issues and questions:
1. Check the documentation in the `docs/` folder
2. Review the troubleshooting section above
3. Check the project's issue tracker
4. Contact the development team

## 🎉 Next Steps

Now that you have the project running:

1. **Explore the codebase** - Familiarize yourself with the architecture
2. **Read the documentation** - Check out other guides in the `docs/` folder
3. **Create a test portfolio** - Try the user workflow
4. **Develop a theme** - Follow the theme development guide
5. **Contribute** - Help improve the project!

---

**Happy coding! 🚀**
