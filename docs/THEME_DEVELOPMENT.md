# 🎨 Theme Development Guide

This guide explains how to create, manage, and maintain themes in Profolify with our clean, scalable modular CSS architecture.

## ⚡ Quick Start (TL;DR)

```bash
# 1. Create new theme
node scripts/scaffold-theme.js my-awesome-theme

# 2. Customize CSS files in themes/my-awesome-theme/components/
# 3. Create React components in themes/my-awesome-theme/components/
# 4. Register theme in themes/theme-registry.ts

# 5. Compile and test
npm run sync-themes
npm run dev

# Done! 🎉
```

**Key Features:**
- 🚀 **One-command theme creation** - No manual setup required
- 🔄 **Auto-compilation** - Universal compiler detects all themes
- 📦 **Modular architecture** - Each component has its own CSS file
- ♾️ **Infinite scalability** - Add unlimited themes without configuration

## 📁 Theme Architecture

### Modular CSS Structure (Recommended)
Profolify uses a **modular CSS architecture** for better maintainability and scalability:

- **Main CSS File**: `themes/[theme-id]/[theme-id]-modular.css` (imports all components)
- **Component CSS Files**: `themes/[theme-id]/components/*.css` (individual section styles)
- **Public CSS**: `public/themes/[theme-id]/[theme-id]-modular.css` (automatically synced)
- **Components**: `themes/[theme-id]/components/*.tsx` (React components)

### Benefits of Modular Architecture
- ✅ **Easy Maintenance**: Edit specific section styles independently
- ✅ **Better Organization**: Clear separation of concerns
- ✅ **Scalable Development**: Multiple developers can work on different sections
- ✅ **Auto-Compilation**: Universal compiler detects and compiles all themes
- ✅ **Zero Configuration**: No manual setup required for new themes

### Scalable Theme System
The new architecture automatically handles:
- **Theme Detection**: Finds all themes with modular structure
- **Universal Compilation**: Single script compiles all themes
- **Auto-Sync**: Compiled CSS automatically synced to public directory
- **Theme Scaffolding**: Generate new themes with one command

## 🚀 Complete Theme Creation Guide

### Step 1: Generate Theme Structure

Use the scaffolding utility to create a complete theme structure:

```bash
# Create a new theme (replace 'my-awesome-theme' with your theme name)
node scripts/scaffold-theme.js my-awesome-theme

# Output:
# 🎨 Creating new theme: my-awesome-theme
# 📄 Creating component files...
#    ✅ navbar.css
#    ✅ hero.css
#    ✅ about.css
#    ✅ experience.css
#    ✅ skills.css
#    ✅ projects.css
#    ✅ contact.css
#    ✅ footer.css
#    ✅ my-awesome-theme-modular.css
# 🎉 Theme 'my-awesome-theme' created successfully!
```

**Theme Naming Rules:**
- Must be lowercase
- Start with a letter
- Only letters, numbers, and hyphens allowed
- Examples: `elegant-portfolio`, `modern-dark`, `creative-minimal`, `corporate-blue`

### Step 2: Customize Your Theme Styles

Navigate to your theme directory and customize the CSS files:

```bash
cd themes/my-awesome-theme/components/
```

**Edit each component file:**

#### `navbar.css` - Navigation Bar
```css
/* my-awesome-theme Theme - Navbar Component */

.theme-my-awesome-theme-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  /* Add your custom navbar styles */
}

.theme-my-awesome-theme-navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  /* Customize brand styling */
}
```

#### `hero.css` - Hero Section
```css
/* my-awesome-theme Theme - Hero Component */

.theme-my-awesome-theme-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  /* Add your hero background and layout */
}

.theme-my-awesome-theme-hero-title {
  font-size: 3rem;
  font-weight: 800;
  color: white;
  /* Customize title styling */
}
```

Continue customizing all component files (`about.css`, `experience.css`, `skills.css`, `projects.css`, `contact.css`, `footer.css`).

### Step 3: Create React Components

Create React components for each section in `themes/my-awesome-theme/components/`:

#### Main Theme Component (`MyAwesomeTheme.tsx`)
```typescript
"use client";
import { ProfolifyThemeProps } from "@/lib/types";
import { MyAwesomeNavbar } from "./MyAwesomeNavbar";
import { MyAwesomeHero } from "./MyAwesomeHero";
import { MyAwesomeAbout } from "./MyAwesomeAbout";
import { MyAwesomeExperience } from "./MyAwesomeExperience";
import { MyAwesomeSkills } from "./MyAwesomeSkills";
import { MyAwesomeProjects } from "./MyAwesomeProjects";
import { MyAwesomeContact } from "./MyAwesomeContact";
import { MyAwesomeFooter } from "./MyAwesomeFooter";

export function MyAwesomeTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-my-awesome-theme-root">
            <MyAwesomeNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <MyAwesomeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <MyAwesomeAbout isEditing={isEditing} serverData={serverData} />
                <MyAwesomeExperience isEditing={isEditing} serverData={serverData} />
                <MyAwesomeSkills isEditing={isEditing} serverData={serverData} />
                <MyAwesomeProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <MyAwesomeContact isEditing={isEditing} serverData={serverData} />
            </main>
            <MyAwesomeFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
}
```

#### Individual Component Example (`MyAwesomeHero.tsx`)
```typescript
"use client";
import { ProfolifyThemeProps } from "@/lib/types";

export function MyAwesomeHero({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <section className="theme-my-awesome-theme-hero">
            <div className="theme-my-awesome-theme-hero-container">
                <h1 className="theme-my-awesome-theme-hero-title">
                    {isEditing ? (
                        <span contentEditable suppressContentEditableWarning>
                            {serverData?.name || "Your Name"}
                        </span>
                    ) : (
                        serverData?.name || "Your Name"
                    )}
                </h1>
                <p className="theme-my-awesome-theme-hero-subtitle">
                    {isEditing ? (
                        <span contentEditable suppressContentEditableWarning>
                            {serverData?.title || "Your Title"}
                        </span>
                    ) : (
                        serverData?.title || "Your Title"
                    )}
                </p>
            </div>
        </section>
    );
}
```

### Step 4: Compile and Test Your Theme

Compile your theme using the universal compiler:

```bash
# Compile all themes (including your new one)
npm run sync-themes

# Output:
# 🚀 Starting universal theme compilation...
# 📋 Found 3 modular theme(s):
#    - creative-minimalist
#    - modern
#    - my-awesome-theme
# 🎨 Compiling my-awesome-theme theme...
# ✅ Compiled: themes/my-awesome-theme/my-awesome-theme-compiled.css
```

### Step 5: Register Your Theme

Add your theme to `themes/theme-registry.ts`:

```typescript
import React from 'react';

// Import theme components
import { ModernTheme } from './modern/components/ModernTheme';
import { CreativeMinimalistTheme } from './creative-minimalist/components/CreativeMinimalistTheme';
import { MyAwesomeTheme } from './my-awesome-theme/components/MyAwesomeTheme'; // Add this line

export const THEME_REGISTRY: ThemeConfig[] = [
  {
    id: 'modern-theme-v1',
    name: 'Modern',
    description: 'A sleek, modern design with gradient backgrounds and smooth animations',
    cssFile: '/themes/modern/modern-compiled.css',
    sourceCssFile: 'themes/modern/modern-compiled.css',
    component: ModernTheme,
    category: 'modern',
    version: '1.0.0',
    author: 'Profolify Team',
  },
  {
    id: 'creative-theme-v1',
    name: 'Creative Minimalist',
    description: 'Clean, minimalist design focused on content and readability',
    cssFile: '/themes/creative-minimalist/creative-minimalist-compiled.css',
    sourceCssFile: 'themes/creative-minimalist/creative-minimalist-compiled.css',
    component: CreativeMinimalistTheme,
    category: 'minimalist',
    version: '1.0.0',
    author: 'Profolify Team',
  },
  // Add your new theme here
  {
    id: 'my-awesome-theme-v1',
    name: 'My Awesome Theme',
    description: 'An awesome theme with great features and modern design',
    cssFile: '/themes/my-awesome-theme/my-awesome-theme-compiled.css',
    sourceCssFile: 'themes/my-awesome-theme/my-awesome-theme-compiled.css',
    component: MyAwesomeTheme,
    category: 'modern', // Choose: 'modern', 'minimalist', 'creative', 'professional'
    version: '1.0.0',
    author: 'Your Name',
  },
];
```

### Step 6: Test Your Theme

Start the development server to test your theme:

```bash
# Start development server
npm run dev

# Navigate to http://localhost:3000
# Go to dashboard and select your new theme
```

**Testing Checklist:**
- ✅ Theme appears in theme selection
- ✅ All sections render correctly
- ✅ Responsive design works on mobile/tablet/desktop
- ✅ Editing functionality works (contentEditable fields)
- ✅ Image upload functionality works
- ✅ CSS styles load properly
- ✅ No console errors

### Step 7: Final Compilation and Deployment

Before deploying, ensure everything is compiled:

```bash
# Final compilation and sync
npm run sync-themes

# Verify compilation
ls themes/my-awesome-theme/
# Should see: my-awesome-theme-compiled.css

# Verify public sync
ls public/themes/my-awesome-theme/
# Should see: my-awesome-theme-compiled.css
```

## 🎨 Advanced Theme Customization

### Adding Custom Components

You can add additional components beyond the standard ones:

#### 1. Create Custom CSS Component
```bash
# Create custom component CSS
touch themes/my-awesome-theme/components/testimonials.css
```

```css
/* my-awesome-theme Theme - Testimonials Component */

.theme-my-awesome-theme-testimonials {
  padding: 5rem 0;
  background: #f8fafc;
}

.theme-my-awesome-theme-testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.theme-my-awesome-theme-testimonial-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
```

#### 2. Add Import to Main CSS
Edit `themes/my-awesome-theme/my-awesome-theme-modular.css`:

```css
/* Import Component Styles */
@import url('./components/navbar.css');
@import url('./components/hero.css');
@import url('./components/about.css');
@import url('./components/experience.css');
@import url('./components/skills.css');
@import url('./components/projects.css');
@import url('./components/contact.css');
@import url('./components/testimonials.css'); /* Add this line */
@import url('./components/footer.css');
```

#### 3. Create React Component
Create `themes/my-awesome-theme/components/MyAwesomeTestimonials.tsx`:

```typescript
"use client";
import { ProfolifyThemeProps } from "@/lib/types";

export function MyAwesomeTestimonials({ isEditing, serverData }: ProfolifyThemeProps) {
    return (
        <section className="theme-my-awesome-theme-testimonials">
            <div className="theme-my-awesome-theme-testimonials-container">
                <h2 className="theme-my-awesome-theme-testimonials-title">
                    Testimonials
                </h2>
                <div className="theme-my-awesome-theme-testimonials-grid">
                    {/* Add testimonial cards here */}
                </div>
            </div>
        </section>
    );
}
```

#### 4. Add to Main Theme Component
Update `MyAwesomeTheme.tsx`:

```typescript
import { MyAwesomeTestimonials } from "./MyAwesomeTestimonials";

export function MyAwesomeTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-my-awesome-theme-root">
            <MyAwesomeNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <MyAwesomeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <MyAwesomeAbout isEditing={isEditing} serverData={serverData} />
                <MyAwesomeExperience isEditing={isEditing} serverData={serverData} />
                <MyAwesomeSkills isEditing={isEditing} serverData={serverData} />
                <MyAwesomeProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <MyAwesomeTestimonials isEditing={isEditing} serverData={serverData} /> {/* Add this */}
                <MyAwesomeContact isEditing={isEditing} serverData={serverData} />
            </main>
            <MyAwesomeFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
}
```

### Theme Variants

Create multiple variants of your theme:

```bash
# Create variants
node scripts/scaffold-theme.js my-awesome-theme-dark
node scripts/scaffold-theme.js my-awesome-theme-light
node scripts/scaffold-theme.js my-awesome-theme-corporate
```

### Performance Optimization

#### CSS Optimization Tips:
- Use CSS custom properties for consistent theming
- Minimize CSS specificity conflicts
- Use efficient selectors
- Optimize for mobile-first responsive design

```css
/* Use CSS custom properties */
.theme-my-awesome-theme-root {
  --primary-color: #3b82f6;
  --secondary-color: #8b5cf6;
  --text-color: #1f2937;
  --background-color: #ffffff;
}

.theme-my-awesome-theme-btn-primary {
  background: var(--primary-color);
  color: white;
}
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Theme Not Appearing in Selection
```bash
# Check if theme is registered
grep -r "my-awesome-theme" themes/theme-registry.ts

# Ensure compilation was successful
npm run sync-themes
```

#### CSS Not Loading
```bash
# Check if compiled CSS exists
ls themes/my-awesome-theme/my-awesome-theme-compiled.css

# Check if public CSS exists
ls public/themes/my-awesome-theme/my-awesome-theme-compiled.css

# Recompile if missing
npm run sync-themes
```

#### Component Not Rendering
- Check React component imports
- Verify CSS class names match
- Check browser console for errors
- Ensure component is added to main theme component

#### Responsive Issues
- Test on multiple screen sizes
- Use browser dev tools responsive mode
- Check CSS media queries
- Verify mobile-first approach

### Debug Commands

```bash
# Check theme detection
node scripts/compile-themes.js

# Validate theme structure
npm run sync-themes --validate-only

# Check for CSS syntax errors
# Use browser dev tools or CSS linter
```

## 📋 Complete Workflow Summary

### Quick Reference: Creating a New Theme

```bash
# 1. Generate theme structure
node scripts/scaffold-theme.js my-new-theme

# 2. Customize CSS components
# Edit files in themes/my-new-theme/components/

# 3. Create React components
# Create .tsx files in themes/my-new-theme/components/

# 4. Register theme
# Add to themes/theme-registry.ts

# 5. Compile and test
npm run sync-themes
npm run dev

# 6. Deploy
# Theme is ready for production!
```

### File Checklist for New Theme

**Required Files:**
- ✅ `themes/[theme-name]/[theme-name]-modular.css` (main CSS with imports)
- ✅ `themes/[theme-name]/components/navbar.css`
- ✅ `themes/[theme-name]/components/hero.css`
- ✅ `themes/[theme-name]/components/about.css`
- ✅ `themes/[theme-name]/components/experience.css`
- ✅ `themes/[theme-name]/components/skills.css`
- ✅ `themes/[theme-name]/components/projects.css`
- ✅ `themes/[theme-name]/components/contact.css`
- ✅ `themes/[theme-name]/components/footer.css`
- ✅ `themes/[theme-name]/components/[ThemeName]Theme.tsx` (main component)
- ✅ Individual React components for each section

**Auto-Generated Files:**
- ✅ `themes/[theme-name]/[theme-name]-compiled.css` (created by compiler)
- ✅ `public/themes/[theme-name]/[theme-name]-compiled.css` (synced automatically)

## 🎯 Best Practices

### CSS Best Practices

#### 1. Consistent Naming Convention
```css
/* Always prefix with theme name */
.theme-my-theme-section { }
.theme-my-theme-section-container { }
.theme-my-theme-section-title { }
.theme-my-theme-section-content { }
```

#### 2. Mobile-First Responsive Design
```css
/* Base styles for mobile */
.theme-my-theme-hero-title {
  font-size: 2rem;
}

/* Tablet and up */
@media (min-width: 768px) {
  .theme-my-theme-hero-title {
    font-size: 3rem;
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .theme-my-theme-hero-title {
    font-size: 4rem;
  }
}
```

#### 3. Use CSS Custom Properties
```css
.theme-my-theme-root {
  /* Define theme colors */
  --primary: #3b82f6;
  --secondary: #8b5cf6;
  --accent: #10b981;
  --text: #1f2937;
  --background: #ffffff;
  --surface: #f9fafb;

  /* Define spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;

  /* Define typography */
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
}

/* Use the custom properties */
.theme-my-theme-btn-primary {
  background: var(--primary);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
}
```

#### 4. Consistent Component Structure
```css
/* Component wrapper */
.theme-my-theme-[component] {
  /* Layout and positioning */
}

/* Component container */
.theme-my-theme-[component]-container {
  /* Content width and centering */
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Component title */
.theme-my-theme-[component]-title {
  /* Title styling */
}

/* Component content */
.theme-my-theme-[component]-content {
  /* Content styling */
}
```

### React Component Best Practices

#### 1. Consistent Component Structure
```typescript
"use client";
import { ProfolifyThemeProps } from "@/lib/types";

export function MyThemeComponent({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    // Component logic here

    return (
        <section className="theme-my-theme-component">
            <div className="theme-my-theme-component-container">
                {/* Component content */}
            </div>
        </section>
    );
}
```

#### 2. Standard Section Behavior Patterns

**IMPORTANT: All themes must follow these consistent behavior patterns:**

##### **Required Sections (Always Show with Dummy Data)**
These sections should always be visible, even when empty, with meaningful default content:

```typescript
// ✅ CORRECT: Experience Component
export function MyThemeExperience({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;

    // Always show at least one dummy experience if none exist
    const defaultExperience: Experience = {
        id: 'default-experience',
        role: 'Software Developer',
        company: 'Tech Company',
        duration: '2022 - Present',
        location: 'Remote',
        description: 'Developing innovative solutions and contributing to exciting projects. Add your experience details here.'
    };

    const experiences = data.experiences && data.experiences.length > 0
        ? data.experiences
        : [defaultExperience];

    return (
        <section className="theme-my-theme-experience">
            {/* Always render - no conditional hiding */}
            {experiences.map(exp => (
                <ExperienceItem key={exp.id} experience={exp} />
            ))}
        </section>
    );
}
```

**Apply this pattern to:**
- **Experience Section**: Show default work experience
- **Skills Section**: Show default skills (JavaScript, React, etc.)
- **Projects Section**: Show default project with placeholder
- **Hero Section**: Always show with placeholder text
- **About Section**: Always show with placeholder text

##### **Contact Section (Always Show with User Email)**
Contact section should always be visible with user's email pre-filled:

```typescript
// ✅ CORRECT: Contact Component
import { useAuthStore } from "@/stores/auth-store";

export function MyThemeContact({ isEditing, serverData }: SectionProps) {
    const { user } = useAuthStore();
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;

    // Use user's email as default if no email is set
    const defaultEmail = user?.email || '<EMAIL>';

    const contactItems = [
        {
            icon: Mail,
            label: "Email",
            value: data.email || defaultEmail, // Always has a value
            field: 'email' as keyof PortfolioData
        },
        {
            icon: Phone,
            label: "Phone",
            value: data.phone || 'Your phone number',
            field: 'phone' as keyof PortfolioData
        }
    ];

    return (
        <section className="theme-my-theme-contact">
            {/* Always show contact section */}
            <div className="contact-info">
                {contactItems.map((item) => {
                    // Always show email, only show phone if it has a real value
                    if (item.field === 'phone' && !item.value && !isEditing) return null;
                    if (item.field === 'phone' && item.value === 'Your phone number' && !isEditing) return null;

                    return <ContactItem key={item.field} item={item} />;
                })}
            </div>

            {/* Social links - only show if there are actual URLs */}
            {(isEditing || socialLinks.some(link => link.url)) && (
                <div className="social-section">
                    <h3>Follow Me</h3>
                    {socialLinks.map(social => (
                        <SocialLink key={social.field} social={social} />
                    ))}
                </div>
            )}
        </section>
    );
}
```

##### **❌ WRONG: Conditional Section Hiding**
```typescript
// ❌ DON'T DO THIS - Inconsistent behavior
export function BadExperience({ isEditing, serverData }: SectionProps) {
    const experiences = data.experiences || [];

    // ❌ This hides the section completely when empty
    if (!isEditing && experiences.length === 0) {
        return null;
    }

    return <section>...</section>;
}
```

#### 3. Standard Behavior Summary

**Theme Consistency Rules:**

| Section | Behavior | Default Content | When to Hide |
|---------|----------|-----------------|--------------|
| **Hero** | Always show | User name, profession, placeholder text | Never |
| **About** | Always show | Placeholder about text | Never |
| **Experience** | Always show | Default work experience | Never |
| **Skills** | Always show | Default skills (JS, React, Node, Git) | Never |
| **Projects** | Always show | Default sample project | Never |
| **Contact** | Always show | User email (from auth) + phone placeholder | Never |
| **Social Links** | Conditional | Empty by default | When no URLs provided |
| **Phone** | Conditional | Placeholder text | When empty and not editing |

**Key Principles:**
1. **Consistency**: All themes follow the same behavior patterns
2. **User-Friendly**: Always show meaningful content, never empty sections
3. **Progressive Enhancement**: Start with defaults, let users customize
4. **Smart Defaults**: Use auth data (email) when available
5. **Optional Elements**: Hide only truly optional items (social links, phone)
6. **Deletable Dummy Data**: All dummy items can be deleted like regular items
7. **Single Dummy Items**: Show only 1 dummy item per section, not multiple

#### 4. Shared Editable Field Styling

**All themes must use shared editable field styling for consistency:**

```typescript
// ✅ CORRECT: Using shared editable field classes
import { EditableText } from "@/components/ui/EditableText";

export function MyThemeComponent({ isEditing, serverData }: SectionProps) {
    return (
        <section className="theme-my-theme-component">
            {/* Hero title with gradient text */}
            <EditableText
                isEditing={isEditing}
                tagName="h1"
                className="theme-my-theme-title editable-field-gradient editable-field-modern"
                initialValue={data.title}
                placeholder="Your Title"
                onSave={(value) => handleUpdate('title', value)}
            />

            {/* Regular text field */}
            <EditableText
                isEditing={isEditing}
                tagName="p"
                className="theme-my-theme-text editable-field editable-field-modern"
                initialValue={data.text}
                placeholder="Your text here"
                onSave={(value) => handleUpdate('text', value)}
            />

            {/* Large description field */}
            <EditableText
                isEditing={isEditing}
                tagName="div"
                className="theme-my-theme-description editable-field-large editable-field-modern"
                initialValue={data.description}
                placeholder="Detailed description"
                onSave={(value) => handleUpdate('description', value)}
            />
        </section>
    );
}
```

**Available Shared Editable Field Classes:**

| Class | Usage | Description |
|-------|-------|-------------|
| `editable-field` | Regular text fields | Standard dotted border styling |
| `editable-field-gradient` | Hero titles, gradient text | No background, preserves gradients |
| `editable-field-inline` | Small inline text | Smaller padding, inline display |
| `editable-field-large` | Descriptions, content areas | Larger padding, more space |
| `editable-field-modern` | Modern theme variant | Blue color scheme |
| `editable-field-creative` | Creative theme variant | Purple color scheme |

**Import Shared Styles in Theme CSS:**
```css
/* In your theme's main CSS file */
@import url('../shared/editable-fields.css');
```

#### 2. Editable Content Pattern
```typescript
// For text content
{isEditing ? (
    <span
        contentEditable
        suppressContentEditableWarning
        className="theme-my-theme-editable-text"
    >
        {serverData?.field || "Default text"}
    </span>
) : (
    serverData?.field || "Default text"
)}

// For image content
{isEditing ? (
    <div className="theme-my-theme-image-upload">
        <input
            type="file"
            onChange={(e) => onImageUpload?.(e.target.files?.[0])}
            accept="image/*"
        />
        {serverData?.image && (
            <img src={serverData.image} alt="Preview" />
        )}
    </div>
) : (
    serverData?.image && (
        <img src={serverData.image} alt="Content" />
    )
)}
```

#### 3. Conditional Section Rendering
```typescript
// Show section only if data exists
{(serverData?.experiences?.length > 0 || isEditing) && (
    <MyThemeExperience
        isEditing={isEditing}
        serverData={serverData}
    />
)}
```

### Performance Best Practices

#### 1. Optimize CSS
- Use efficient selectors
- Minimize nesting depth
- Avoid !important declarations
- Use CSS custom properties for theming

#### 2. Optimize Images
- Use appropriate image formats (WebP, AVIF)
- Implement lazy loading
- Provide responsive images

#### 3. Code Splitting
- Keep components modular
- Import only what's needed
- Use dynamic imports for large components

## 🚀 Advanced Features

### Theme Configuration

Create a theme configuration file for advanced settings:

```typescript
// themes/my-awesome-theme/theme.config.ts
export const MyAwesomeThemeConfig = {
  name: 'My Awesome Theme',
  version: '1.0.0',
  author: 'Your Name',
  description: 'An awesome theme with great features',
  category: 'modern',
  features: [
    'responsive-design',
    'dark-mode-ready',
    'animation-support',
    'custom-colors'
  ],
  customization: {
    colors: {
      primary: '#3b82f6',
      secondary: '#8b5cf6',
      accent: '#10b981'
    },
    fonts: {
      heading: 'Inter, sans-serif',
      body: 'Inter, sans-serif'
    },
    spacing: {
      section: '5rem',
      container: '1200px'
    }
  }
};
```

### Theme Variants System

Create multiple variants of your theme:

```bash
# Create base theme
node scripts/scaffold-theme.js awesome-base

# Create variants
node scripts/scaffold-theme.js awesome-dark
node scripts/scaffold-theme.js awesome-light
node scripts/scaffold-theme.js awesome-corporate
```

### Custom Animations

Add custom animations to your theme:

```css
/* themes/my-theme/components/animations.css */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.theme-my-theme-animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.theme-my-theme-animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}
```

## 📚 Resources and References

### Useful Links
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/)
- [Flexbox Guide](https://css-tricks.com/snippets/css/a-guide-to-flexbox/)
- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/Using_CSS_custom_properties)
- [Responsive Design Patterns](https://web.dev/responsive-web-design-basics/)

### Design Inspiration
- [Dribbble](https://dribbble.com/tags/portfolio)
- [Behance](https://www.behance.net/search/projects?search=portfolio)
- [Awwwards](https://www.awwwards.com/websites/portfolio/)

### Color Palettes
- [Coolors](https://coolors.co/)
- [Adobe Color](https://color.adobe.com/)
- [Tailwind CSS Colors](https://tailwindcss.com/docs/customizing-colors)

---

**Happy Theme Development! 🎨✨**

Need help? Check the troubleshooting section or create an issue in the repository.
    sourceCssFile: 'themes/elegant-portfolio-v1/elegant-portfolio-v1-modular.css',
    component: ElegantPortfolioTheme,
    category: 'professional',
    version: '1.0.0',
    author: 'Your Name',
  },
];
```

### 3. Sync CSS Files

```bash
# Sync CSS files to public directory
npm run sync-themes

# Validate theme files
npm run validate-themes
```

## 🏗️ Theme Structure

### Recommended Modular Structure

```
themes/
├── theme-registry.ts
├── [theme-id]/
│   ├── [theme-id]-modular.css  # Main CSS file (imports all components)
│   └── components/
│       ├── navbar.css          # Navigation styles
│       ├── hero.css            # Hero section styles
│       ├── about.css           # About section styles
│       ├── experience.css      # Experience section styles
│       ├── skills.css          # Skills section styles
│       ├── projects.css        # Projects section styles
│       ├── contact.css         # Contact section styles
│       ├── footer.css          # Footer styles
│       ├── [Theme]Theme.tsx    # Main theme component
│       ├── [Theme]Navbar.tsx   # Navigation component
│       ├── [Theme]Hero.tsx     # Hero section
│       ├── [Theme]About.tsx    # About section
│       ├── [Theme]Experience.tsx
│       ├── [Theme]Skills.tsx
│       ├── [Theme]Projects.tsx
│       ├── [Theme]Contact.tsx
│       └── [Theme]Footer.tsx
```

### Example: Creative Minimalist Theme

```
themes/
├── theme-registry.ts
├── creative-minimalist/
│   ├── creative-minimalist-modular.css  # Main CSS with @imports
│   └── components/
│       ├── navbar.css                   # Navigation styles
│       ├── hero.css                     # Hero section styles
│       ├── about.css                    # About section styles
│       ├── experience.css               # Experience section styles
│       ├── skills.css                   # Skills section styles
│       ├── projects.css                 # Projects section styles
│       ├── contact.css                  # Contact section styles
│       ├── footer.css                   # Footer styles
│       ├── CreativeMinimalistTheme.tsx  # Main theme component
│       ├── CreativeNavbar.tsx           # Navigation component
│       ├── CreativeHero.tsx             # Hero section
│       ├── CreativeAbout.tsx            # About section
│       ├── CreativeExperience.tsx       # Experience section
│       ├── CreativeSkills.tsx           # Skills section
│       ├── CreativeProjects.tsx         # Projects section
│       ├── CreativeContact.tsx          # Contact section
│       └── CreativeFooter.tsx           # Footer component
```

## 🎨 CSS Guidelines

### 1. Use Theme-Specific Classes
All CSS classes must be prefixed with your theme ID:

```css
/* ✅ Good */
.theme-elegant-portfolio-hero { }
.theme-elegant-portfolio-btn { }

/* ❌ Bad */
.hero { }
.btn { }
```

### 2. Modular CSS Implementation

#### Step 1: Create Component CSS Files

Create separate CSS files for each major section in the `components/` folder:

```css
/* themes/your-theme/components/navbar.css */
.theme-your-theme-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  z-index: 1000;
}

.theme-your-theme-navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}
```

```css
/* themes/your-theme/components/hero.css */
.theme-your-theme-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.theme-your-theme-hero-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
}
```

```css
/* themes/your-theme/components/experience.css */
.theme-your-theme-experience {
  padding: 5rem 0;
  background: #f9fafb;
}

.theme-your-theme-experience-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
}

/* Editable field styles for consistent editing experience */
.theme-your-theme-experience-field-input[contenteditable="true"] {
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
}

.theme-your-theme-experience-field-input[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.theme-your-theme-experience-field-input[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
```

#### Step 2: Create Main CSS File with Imports

Create a main modular CSS file that imports all component styles:

```css
/* themes/your-theme/your-theme-modular.css */

/* Import Component Styles */
@import url('./components/navbar.css');
@import url('./components/hero.css');
@import url('./components/about.css');
@import url('./components/experience.css');
@import url('./components/skills.css');
@import url('./components/projects.css');
@import url('./components/contact.css');
@import url('./components/footer.css');

/* Base styles and reset */
.theme-your-theme-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #111827;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
  overflow-x: hidden;
}

.theme-your-theme-root *,
.theme-your-theme-root *::before,
.theme-your-theme-root *::after {
  box-sizing: inherit;
}

/* Common utilities */
.theme-your-theme-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-your-theme-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.theme-your-theme-btn-primary {
  background: #3b82f6;
  color: white;
}

.theme-your-theme-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-2px);
}
```

### 3. Follow Component Structure
Organize CSS by components:

```css
/* Navbar Styles */
.theme-elegant-portfolio-navbar { }
.theme-elegant-portfolio-navbar-brand { }

/* Hero Styles */
.theme-elegant-portfolio-hero { }
.theme-elegant-portfolio-hero-title { }

/* Button Utilities */
.theme-elegant-portfolio-btn { }
.theme-elegant-portfolio-btn-primary { }
```

### 4. Responsive Design
Use mobile-first responsive design:

```css
.theme-elegant-portfolio-hero {
  padding: 2rem 1rem;
}

@media (min-width: 768px) {
  .theme-elegant-portfolio-hero {
    padding: 4rem 2rem;
  }
}
```

### 5. CSS File Management for Export Consistency

#### Important: CSS File Synchronization

The theme CSS files must be properly synchronized between the `themes/` directory and the `public/themes/` directory to ensure consistent export functionality:

1. **Development Location**: `themes/your-theme/your-theme.css`
2. **Export Location**: `public/themes/your-theme/your-theme.css`

#### Automatic Synchronization

Use the sync script to copy CSS files to the public directory:

```bash
npm run sync-themes
```

This ensures that:
- Live editor uses the development CSS files
- Static export uses the public CSS files
- Both versions remain identical

#### Manual CSS Management

If you need to manually manage CSS files:

1. **After editing CSS**: Always run `npm run sync-themes`
2. **Before deployment**: Verify all CSS files are synced
3. **For new themes**: Ensure CSS files are copied to public directory

## ⚛️ Component Guidelines

### 1. Use Pure HTML/CSS
All components must use pure HTML elements with CSS classes:

```tsx
// ✅ Good
<button className="theme-elegant-portfolio-btn theme-elegant-portfolio-btn-primary">
  Click me
</button>

// ❌ Bad (ShadCN components won't export properly)
<Button variant="primary">Click me</Button>
```

### 2. Component Structure
Each component should follow this pattern:

```tsx
"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";

export function ElegantPortfolioHero({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;

    return (
        <section className="theme-elegant-portfolio-hero">
            <div className="theme-elegant-portfolio-container">
                <EditableText
                    isEditing={isEditing}
                    tagName="h1"
                    className="theme-elegant-portfolio-hero-title"
                    initialValue={data.userName}
                    onSave={(value) => {/* handle save */}}
                />
            </div>
        </section>
    );
}
```

## 🔧 Development Workflow

### 1. Development
```bash
# Start development server
npm run dev

# Edit component CSS files directly:
# themes/your-theme/components/navbar.css
# themes/your-theme/components/hero.css
# themes/your-theme/components/experience.css
# etc.

# Changes are reflected immediately - no compilation needed!
```

### 2. Testing Export
```bash
# Sync CSS files to public directory before testing export
npm run sync-themes

# Test export functionality in the app
```

### 3. Build
```bash
# CSS files are automatically synced during build
npm run build
```

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run create-theme` | Interactive theme creation wizard |
| `npm run compile-css` | Compile modular CSS files into single files for export |
| `npm run sync-themes` | Compile CSS + sync files from source to public directory |
| `npm run validate-themes` | Validate theme files and structure |
| `npm run dev` | Start development server |
| `npm run build` | Build for production (auto-compiles and syncs themes) |

## 🔄 Dual CSS Architecture Workflow

### Development vs Export CSS System

Profolify uses a **dual CSS architecture** to optimize both development experience and export functionality:

- **Development**: Uses modular CSS with `@import` statements for easy editing
- **Export**: Uses compiled CSS with all styles inline for static site compatibility

### Development Workflow

1. **Edit Component CSS Files Directly**
   ```bash
   # Edit individual component files - changes are immediate
   themes/your-theme/components/navbar.css
   themes/your-theme/components/hero.css
   themes/your-theme/components/experience.css
   # ... etc
   ```

2. **Compile and Sync for Export Testing**
   ```bash
   npm run sync-themes
   ```
   This automatically:
   - Compiles modular CSS into a single file
   - Syncs the compiled CSS to `public/themes/your-theme/`

3. **Test Changes**
   ```bash
   npm run dev
   ```

### CSS Compilation Process

The system automatically handles CSS compilation:

```bash
# Manual compilation (if needed)
npm run compile-css

# Full sync (compile + sync to public)
npm run sync-themes

# Production build (compile + sync + build)
npm run build
```

### How It Works

1. **Modular CSS** (`[theme-id]-modular.css`):
   - Contains `@import` statements for component files
   - Used during development for live editing
   - Browser handles imports natively

2. **Compiled CSS** (`[theme-id]-compiled.css`):
   - Single file with all component styles inline
   - Used for static exports
   - No external dependencies

### Benefits of This Approach

- ✅ **Best of Both Worlds**: Easy development + reliable exports
- ✅ **Immediate Changes**: Edit CSS and see changes instantly in development
- ✅ **Export Compatibility**: Compiled CSS works in static sites
- ✅ **Automatic Process**: Compilation happens automatically
- ✅ **Easy Debugging**: Each component's styles are in separate files
- ✅ **Better Organization**: Clear separation of section styles
- ✅ **Scalable**: Easy to add new sections or modify existing ones
- ✅ **Maintainable**: Find and edit specific styles quickly
- ✅ **Collaborative**: Multiple developers can work on different sections

## 🎯 Key Guidelines for Theme Development

### CSS Structure Best Practices

1. **Use Modular CSS Files**: Create separate CSS files for each major section
2. **Theme-Specific Classes**: Always prefix classes with `theme-[theme-id]-`
3. **Editable Field Styling**: Use consistent dotted border styling for editable fields
4. **Responsive Design**: Use mobile-first approach with proper breakpoints
5. **Component Organization**: Keep related styles together in component files

### Editable Field Styling (Important!)

For consistent user experience, all editable fields should use this styling pattern:

```css
/* Standard editable field styling */
.theme-your-theme-field-input[contenteditable="true"] {
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  outline: none;
}

.theme-your-theme-field-input[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.theme-your-theme-field-input[contenteditable="true"]:focus {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-your-theme-field-input[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}
```

This ensures users can clearly see which fields are editable across all themes.

### File Organization Tips

1. **Keep CSS files focused**: Each component CSS file should only contain styles for that specific section
2. **Use consistent naming**: Follow the pattern `[section].css` (e.g., `hero.css`, `experience.css`)
3. **Import order matters**: Import CSS files in logical order in your main modular CSS file
4. **Comment your code**: Add comments to explain complex styles or design decisions

## 🔍 Troubleshooting

### Images Not Loading in Export
1. **Check Image Components**: Ensure you're using `PortfolioImage` component, not regular `<img>` tags
2. **Verify Export Context**: Check that `useIsExport()` hook is properly imported and used
3. **Test Image URLs**: Verify images work in live site before testing export
4. **Check Console Logs**: Look for image fixing logs during export process

### Mobile Menu Not Working in Export
1. **Check Data Attributes**: Ensure button has `data-mobile-menu-toggle="true"`
2. **Verify Target ID**: Menu element must have ID matching `data-target` attribute
3. **Test Export Context**: Verify `isExport` flag is properly detected
4. **Check CSS Classes**: Ensure mobile menu has proper show/hide CSS

### CSS Not Loading in Export
1. **Compile and Sync CSS**: Run `npm run sync-themes`
2. **Check Compiled CSS**: Verify `[theme-id]-compiled.css` exists in theme directory
3. **Check Public CSS**: Ensure CSS file exists in `public/themes/[theme-id]/`
4. **Verify Theme Registry**: Check theme is registered with correct CSS file path
5. **Test CSS URL**: Test CSS fetch URL in browser
6. **Check Component Files**: Ensure all component CSS files exist and are valid

### CSS Compilation Issues
1. **Missing Component Files**: Check all component CSS files exist in `components/` folder
2. **Invalid CSS Syntax**: Validate CSS syntax in component files
3. **Import Errors**: Verify modular CSS file has correct `@import` statements
4. **File Permissions**: Ensure script has write permissions for compiled CSS file

### Layout Issues in Export
1. **Check Theme Root**: Ensure theme has proper root container with correct class
2. **Verify CSS Reset**: Check if custom CSS conflicts with export CSS reset
3. **Test Responsive Design**: Verify layout works on different screen sizes
4. **Check Margins/Padding**: Ensure no unwanted spacing in exported site

### Components Not Rendering
1. Ensure all components use pure HTML/CSS
2. Check CSS class names match between components and CSS
3. Verify theme component is properly imported and exported
4. Test with Live DOM Capture system

### Build Errors
1. Run `npm run validate-themes` to check for issues
2. Ensure all required files exist
3. Check TypeScript errors in components
4. Verify all imports are correct

## 🧪 Testing Your Theme with Live DOM Capture

### Pre-Export Testing Checklist

**✅ Live Site Testing:**
- [ ] Theme loads correctly in development (`npm run dev`)
- [ ] All images display properly (hero, projects, about)
- [ ] Mobile menu works on live site
- [ ] Responsive design works on all screen sizes
- [ ] All interactive elements function correctly

**✅ Export Preparation:**
- [ ] Run `npm run sync-themes` to sync CSS files
- [ ] Verify theme is registered in `theme-registry.ts`
- [ ] Check that all images use `PortfolioImage` component
- [ ] Ensure mobile menu follows export-compatible pattern

### Export Testing Process

**Step 1: Export Generation**
1. Navigate to dashboard
2. Click "Export Site" button
3. Check browser console for export logs
4. Verify ZIP file downloads successfully

**Step 2: Export Validation**
1. Extract ZIP file to local folder
2. Open `index.html` in browser
3. Check browser Network tab for image requests
4. Verify no `/_next/image` URLs in network requests
5. Test mobile menu functionality
6. Check responsive design on different screen sizes

**Step 3: Cross-Browser Testing**
1. Test exported site in Chrome, Firefox, Safari
2. Verify images load correctly in all browsers
3. Test mobile menu on touch devices
4. Check layout consistency across browsers

### Debug Console Logs

During export, you should see logs like:
```
🎨 Starting live DOM capture export for: your-theme
🖼️ Found 3 images to process
🔧 Found Next.js optimized image, fixing...
✅ Fixed Next.js image URL: https://res.cloudinary.com/...
📱 Found mobile menu toggles: 1
📱 Toggle 1: target=your-theme-mobile-menu, menu found=true
✅ Live DOM capture export completed successfully
```

### Common Export Issues and Solutions

**Issue: Images show as broken in exported site**
- **Solution**: Use `PortfolioImage` component instead of regular `<img>` tags
- **Check**: Verify `isEditing` prop is passed to `PortfolioImage`

**Issue: Mobile menu doesn't work in exported site**
- **Solution**: Follow export-compatible mobile menu pattern
- **Check**: Ensure `data-mobile-menu-toggle` and `data-target` attributes are set

**Issue: Layout has unwanted margins in exported site**
- **Solution**: Export system includes CSS reset, check for conflicting styles
- **Check**: Verify theme root container has proper CSS classes

**Issue: CSS styles not applied in exported site**
- **Solution**: Run `npm run sync-themes` and verify CSS file exists in public directory
- **Check**: Ensure theme is properly registered in theme registry

## 📚 Best Practices

1. **Consistent Naming**: Use kebab-case for theme IDs and CSS classes
2. **Component Isolation**: Each theme should be completely self-contained
3. **Responsive Design**: Always test on mobile, tablet, and desktop
4. **Export Testing**: Always test export functionality before releasing
5. **Version Control**: Use semantic versioning for theme updates
6. **Documentation**: Document any special features or requirements

## 🎯 Export Compatibility - Live DOM Capture System

Profolify uses a revolutionary **Live DOM Capture Export System** that captures the actual rendered DOM from your live theme, ensuring pixel-perfect exports. To ensure your theme works perfectly with this system:

### Core Requirements

1. **Use Pure HTML/CSS**: No ShadCN or external component libraries
2. **Self-contained CSS**: All styles must be in the theme's CSS file
3. **PortfolioImage Component**: Use `PortfolioImage` for all images to ensure export compatibility
4. **Mobile Menu Compatibility**: Follow mobile menu patterns for export functionality
5. **Theme Root Container**: Use proper theme root class naming

### Image Handling for Export

**✅ Correct Image Usage:**
```tsx
import { PortfolioImage } from "@/components/ui/PortfolioImage";

// Use PortfolioImage component for all images
<PortfolioImage
    isEditing={isEditing}
    src={data.profileImageUrl || 'https://placehold.co/400x400/f3f4f6/6b7280?text=Profile'}
    alt={data.userName}
    width={400}
    height={400}
    className="theme-your-theme-hero-image"
/>
```

**❌ Incorrect Image Usage:**
```tsx
// Don't use regular img tags directly
<img src={data.profileImageUrl} alt={data.userName} />

// Don't use Next.js Image component directly
<Image src={data.profileImageUrl} alt={data.userName} width={400} height={400} />
```

### Mobile Menu Export Compatibility

**✅ Export-Compatible Mobile Menu:**
```tsx
import { useIsExport } from "@/contexts/ExportContext";

export function YourThemeNavbar({ isEditing, serverData }: SectionProps) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const isExport = useIsExport();

    return (
        <nav className="theme-your-theme-navbar">
            {/* Mobile menu toggle button */}
            <button
                type="button"
                onClick={isExport ? undefined : () => setIsMobileMenuOpen(!isMobileMenuOpen)}
                aria-label="Toggle menu"
                className="theme-your-theme-navbar-mobile-btn"
                {...(isExport && {
                    'data-mobile-menu-toggle': 'true',
                    'data-target': 'your-theme-mobile-menu'
                })}
            >
                <svg>...</svg>
            </button>

            {/* Mobile menu */}
            <div
                id="your-theme-mobile-menu"
                className={`theme-your-theme-navbar-mobile-menu ${!isExport && isMobileMenuOpen ? 'active' : ''}`}
                style={isExport ? { display: 'none' } : undefined}
            >
                <nav>...</nav>
            </div>
        </nav>
    );
}
```

### Theme Root Container

**✅ Proper Theme Root:**
```tsx
export function YourTheme({ isEditing, serverData, onImageUpload }: ThemeProps) {
    return (
        <div className="theme-your-theme-root">
            <YourThemeNavbar isEditing={isEditing} serverData={serverData} />
            <YourThemeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
            {/* ... other sections */}
        </div>
    );
}
```

### CSS Classes for Export

Ensure your CSS follows the theme-specific naming pattern:

```css
/* Theme root container */
.theme-your-theme-root {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Mobile menu styles */
.theme-your-theme-navbar-mobile-menu {
    display: none;
}

.theme-your-theme-navbar-mobile-menu.active {
    display: block;
}
```

## 🚀 Advanced Features

### Custom Animations
```css
@keyframes theme-elegant-portfolio-fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.theme-elegant-portfolio-hero {
  animation: theme-elegant-portfolio-fade-in 0.6s ease-out;
}
```



### Print Styles
```css
@media print {
  .theme-elegant-portfolio-navbar {
    display: none;
  }
}
```

## 🎯 Live DOM Capture System Benefits

The Live DOM Capture Export System provides significant advantages for theme developers:

### ✅ Theme Development Benefits

**1. Perfect Export Fidelity**
- Your exported themes match the live site exactly, pixel for pixel
- No need to maintain separate export templates
- Complex layouts and animations are preserved automatically

**2. Future-Proof Architecture**
- New themes work with export system automatically
- No additional export logic required per theme
- System scales infinitely with new theme additions

**3. Simplified Development**
- Focus on creating beautiful themes, not export compatibility
- Standard React components work seamlessly
- No special export considerations beyond basic guidelines

**4. Comprehensive Image Handling**
- 6-layer image fixing system ensures all images work in exports
- Automatic conversion of Next.js optimized URLs to direct URLs
- Robust error handling with automatic fallbacks

**5. Native Mobile Menu Support**
- Export system automatically adds JavaScript for mobile menus
- No external dependencies required
- Touch-friendly navigation in exported sites

### ✅ Technical Excellence

**Performance:**
- Sub-second export generation
- Client-side processing eliminates server timeouts
- Instant feedback and debugging capabilities

**Reliability:**
- Multiple fallback layers for error handling
- Comprehensive logging for debugging
- Cross-browser compatibility guaranteed

**Maintainability:**
- Single codebase for live and export versions
- Clean separation of concerns
- Easy to extend and modify

### ✅ User Experience

**For Theme Developers:**
- Simple development workflow
- Immediate export testing
- Clear debugging information

**For End Users:**
- Professional-quality exported sites
- Perfect mobile responsiveness
- Self-contained, deployable packages

This revolutionary system ensures that your themes provide the best possible experience for both development and export, making Profolify the premier platform for portfolio creation.
