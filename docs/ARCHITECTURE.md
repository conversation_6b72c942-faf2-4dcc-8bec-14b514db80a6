# 🏗️ Architecture Overview

This document provides a comprehensive overview of Portfolio Builder's system architecture, design patterns, and technical decisions.

## 🎯 Core Philosophy

Portfolio Builder is built on the **Jamstack architecture** with a focus on:
- **Separation of Concerns**: Frontend and backend services are decoupled
- **Scalability**: Designed to handle growth in users and features
- **Performance**: Optimized for speed and user experience
- **Maintainability**: Clean, modular code structure

## 🏛️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Next.js)     │    │   Services      │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Dashboard     │◄──►│ • Firebase      │    │ • Cloudinary    │
│ • Portfolio     │    │   - Auth        │    │   - Images      │
│   Editor        │    │   - Firestore   │    │   - Optimization│
│ • Theme System  │    │ • API Routes    │    │ • Vercel        │
│ • Export System │    │ • Middleware    │    │   - Hosting     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Technology Stack

### Frontend Stack
- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS v4 + Shadcn/UI
- **State Management**: 
  - TanStack Query (server state)
  - React Context + useReducer (editor state)
  - <PERSON><PERSON><PERSON> (auth state)
- **UI Components**: Custom components with Shadcn/UI base

### Backend Stack
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth with Google OAuth
- **File Storage**: Cloudinary
- **API**: Next.js API Routes
- **Deployment**: Vercel

### Development Tools
- **TypeScript**: Full type safety
- **ESLint**: Code linting
- **Tailwind CSS**: Utility-first styling
- **JSZip**: Client-side ZIP generation

## 📊 Data Architecture

### Firestore Collections

```typescript
// portfolios/{userId}
interface PortfolioData {
  uid: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  userName: string;
  profession: string;
  about?: string;
  bio?: string;
  qualifications?: string;
  profileImageUrl?: string;
  resumeUrl?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
  socials: SocialLinks;
  contactEmail: string;
  email?: string;
  phone?: string;
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
}
```

### Default Data System
New portfolios are created with sample data:
- **Experience**: One default job entry
- **Skills**: One default skill
- **Projects**: One sample project
- **Contact**: Pre-filled email from Google auth

## 🎨 Theme Architecture

### Theme Structure
```
themes/
├── [theme-name]/
│   ├── components/           # React components
│   │   ├── ThemeRoot.tsx    # Main theme component
│   │   ├── Hero.tsx         # Hero section
│   │   ├── About.tsx        # About section
│   │   ├── Experience.tsx   # Experience section
│   │   ├── Skills.tsx       # Skills section
│   │   ├── Projects.tsx     # Projects section
│   │   └── Contact.tsx      # Contact section
│   ├── [theme-name]-modular.css  # Main CSS file
│   └── components/          # Component-specific CSS
│       ├── hero.css
│       ├── about.css
│       ├── experience.css
│       ├── skills.css
│       ├── projects.css
│       └── contact.css
```

### Theme System Features
- **Modular CSS**: Each component has its own CSS file
- **Universal Compiler**: Automatically detects and compiles all themes
- **Auto-Sync**: Compiled CSS synced to public directory
- **Theme Registry**: Centralized theme registration
- **Scaffolding**: One-command theme creation

## 🔄 State Management

### Editor Context Pattern
```typescript
// Smart Container Pattern
const EditorContext = createContext<EditorContextType | null>(null);

// Reducer for complex state updates
function editorReducer(state: EditorState, action: EditorAction): EditorState {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return { ...state, formData: { ...state.formData, [action.payload.field]: action.payload.value } };
    case 'UPDATE_PROJECT':
      // Complex project updates
    case 'ADD_PROJECT':
      // Add new project
    default:
      return state;
  }
}
```

### State Layers
1. **Server State**: TanStack Query for data fetching/caching
2. **Editor State**: React Context for portfolio editing
3. **Auth State**: Zustand for authentication
4. **Export State**: Context for export operations

## 📤 Export System Architecture

### Live DOM Capture System
The revolutionary export system works in multiple layers:

```typescript
// Primary: Live DOM Capture
export async function exportWithLiveDOMCapture(portfolioData: PortfolioData) {
  // 1. Set export context flags
  window.__PORTFOLIO_EXPORT__ = true;
  document.documentElement.setAttribute('data-export', 'true');
  
  // 2. Capture rendered DOM
  const themeRoot = document.querySelector('[data-theme-root]');
  const clonedTheme = themeRoot.cloneNode(true);
  
  // 3. Clean and optimize
  cleanDOMForExport(clonedTheme);
  fixNextJSImagesInDOM(clonedTheme);
  
  // 4. Generate complete HTML
  const completeHTML = generateCompleteHTMLDocument(portfolioData, clonedTheme.outerHTML, cssContent);
  
  // 5. Create ZIP download
  const zip = new JSZip();
  zip.file('index.html', completeHTML);
  // Download...
}
```

### Export Methods
1. **Live DOM Capture** (Primary): Captures actual rendered portfolio
2. **Template-based Export** (Fallback): Generates HTML from data
3. **Smart Detection**: Automatically chooses best method

## 🔐 Security Architecture

### Authentication Flow
```
User → Google OAuth → Firebase Auth → JWT Token → Protected Routes
```

### Security Measures
- **Firebase Security Rules**: Database-level protection
- **Middleware**: Route protection
- **Server-side Validation**: API route validation
- **CORS Configuration**: Proper cross-origin setup

### Data Protection
- User data isolated by UID
- Published portfolios publicly accessible
- Private data requires authentication

## 🚀 Performance Optimizations

### Frontend Optimizations
- **Next.js App Router**: Optimal routing and caching
- **Image Optimization**: Cloudinary integration
- **Code Splitting**: Automatic with Next.js
- **Static Generation**: For public portfolios

### Backend Optimizations
- **Firestore Indexing**: Optimized queries
- **Caching**: TanStack Query for client-side caching
- **CDN**: Vercel Edge Network

### Export Optimizations
- **Client-side Processing**: No server load
- **Efficient DOM Manipulation**: Minimal cloning
- **Compressed Assets**: Optimized CSS/HTML

## 🔄 Development Workflow

### Theme Development
1. **Scaffold**: `node scripts/scaffold-theme.js theme-name`
2. **Develop**: Create components and styles
3. **Register**: Add to theme registry
4. **Compile**: `npm run sync-themes`
5. **Test**: Local development server

### Feature Development
1. **Plan**: Architecture design
2. **Implement**: Feature development
3. **Test**: Local testing
4. **Document**: Update documentation
5. **Deploy**: Vercel deployment

## 📈 Scalability Considerations

### Current Scalability
- **Firestore**: Handles millions of documents
- **Vercel**: Global edge deployment
- **Cloudinary**: Unlimited image storage
- **Client-side Export**: No server bottlenecks

### Future Scalability
- **Theme Marketplace**: Plugin architecture ready
- **Multi-tenancy**: User isolation already implemented
- **API Rate Limiting**: Can be added to API routes
- **Caching Layers**: Redis can be integrated

## 🔧 Configuration Management

### Environment Variables
- **Firebase**: Authentication and database
- **Cloudinary**: Image management
- **Next.js**: Framework configuration

### Build Configuration
- **TypeScript**: Type checking
- **Tailwind**: CSS compilation
- **Theme Compiler**: Custom CSS processing

## 📊 Monitoring & Analytics

### Current Monitoring
- **Vercel Analytics**: Performance monitoring
- **Firebase Console**: Database monitoring
- **Browser DevTools**: Client-side debugging

### Future Monitoring
- **Error Tracking**: Sentry integration ready
- **User Analytics**: Google Analytics ready
- **Performance Monitoring**: Web Vitals tracking

---

This architecture provides a solid foundation for current needs while being flexible enough to accommodate future growth and feature additions.
