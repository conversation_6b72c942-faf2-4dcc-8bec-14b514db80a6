# Portfolio Builder - Modern Portfolio Creation Platform

<div align="center">
  <img src="./public/logo.png" alt="Portfolio Builder Logo" width="150">
  <h1 align="center">Portfolio Builder</h1>
  <p align="center">
    Create, edit, and publish beautiful, professional portfolios in minutes with zero coding required.
    <br />
    <br />
    <a href="#"><strong>Explore a Demo »</strong></a>
    <br />
    <br />
  </p>
</div>

## ✨ About The Project

Portfolio Builder is a revolutionary portfolio creation platform that empowers professionals to build stunning, responsive portfolios with zero coding required. Built with cutting-edge technology and featuring our groundbreaking **Live DOM Capture Export System**, Portfolio Builder delivers pixel-perfect static websites that work anywhere.

This project represents the next generation of portfolio builders, combining intuitive WYSIWYG editing with professional-grade export capabilities that preserve every detail of your live portfolio.

### 🚀 Revolutionary Features:

*   **Live DOM Capture Export:** Our breakthrough technology captures your actual rendered portfolio, ensuring exported sites match your live portfolio pixel-for-pixel
*   **Real-time Inline Editing:** Click directly on any text to edit it in place with instant visual feedback
*   **Professional Theme System:** Choose from beautifully crafted themes (Modern, Creative Minimalist) with more coming soon
*   **Smart URL Generation:** Automatically creates clean, SEO-friendly URLs from your name (e.g., `/john-smith`)
*   **Universal Static Export:** Download complete, self-contained websites ready for any hosting service
*   **Google Authentication:** Secure, one-click sign-in with your Google account
*   **Cloudinary Integration:** Professional image optimization and delivery for lightning-fast loading
*   **Mobile-First Design:** Every theme is fully responsive and optimized for all devices
*   **Modern Dashboard:** Intuitive portfolio management with preview, edit, and export capabilities
*   **Default Content System:** New portfolios come pre-populated with sample data for immediate editing

---

## 📚 Documentation

### 🌟 Project Overview
- **[🌟 Project Overview](docs/PROJECT_OVERVIEW.md)** - Complete project summary and strategic vision

### 🛠️ Technical Documentation
- **[📖 Getting Started Guide](docs/GETTING_STARTED.md)** - Setup and installation instructions
- **[🏗️ Architecture Overview](docs/ARCHITECTURE.md)** - System design and technical architecture
- **[🎨 Theme Development](docs/THEME_DEVELOPMENT.md)** - Complete guide to creating custom themes
- **[📤 Export System](docs/EXPORT_SYSTEM.md)** - How the Live DOM Capture export works
- **[🔧 API Reference](docs/API_REFERENCE.md)** - Backend API documentation
- **[🚀 Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment instructions

### 📈 Business & Strategy
- **[🚀 Future Enhancements](docs/FUTURE_ENHANCEMENTS.md)** - Product roadmap and planned features
- **[📊 Market Analysis](docs/MARKET_ANALYSIS.md)** - Market research and growth strategy
- **[🎯 Marketing Strategy](docs/MARKETING_STRATEGY.md)** - Comprehensive marketing and content strategy
- **[🎬 Demo Procedures](docs/DEMO_PROCEDURES.md)** - Video scripts and demo guidelines

---

## 🚀 Built With

This project leverages cutting-edge technologies to deliver a premium, scalable experience.

### **Core Technologies:**
*   **Framework:** [Next.js 15](https://nextjs.org/) with App Router for optimal performance
*   **Styling:** [Tailwind CSS v4](https://tailwindcss.com/) & [Shadcn/UI](https://ui.shadcn.com/) for modern design
*   **Database:** [Firestore](https://firebase.google.com/docs/firestore) for real-time data management
*   **Authentication:** [Firebase Authentication](https://firebase.google.com/docs/auth) with Google OAuth
*   **Media Storage:** [Cloudinary](https://cloudinary.com/) for optimized image delivery
*   **Deployment:** [Vercel](https://vercel.com/) for global edge deployment

### **State Management:**
*   **Server State:** [TanStack Query](https://tanstack.com/query/latest) for data fetching and caching
*   **Editor State:** React Context with `useReducer` for portfolio editing
*   **Global State:** [Zustand](https://zustand-demo.pmnd.rs/) for authentication and UI state

### **Export System:**
*   **Live DOM Capture:** Revolutionary client-side HTML capture technology
*   **Static Generation:** Complete, self-contained website packages
*   **Universal Compatibility:** Works with any static hosting service

## 🚀 Quick Start

For detailed setup instructions, see the **[📖 Getting Started Guide](docs/GETTING_STARTED.md)**.

### Prerequisites
*   **Node.js** (v18 or higher)
*   **npm** or **yarn**
*   **Git**

### Installation

1.  **Clone the repository**
    ```bash
    git clone <repository-url>
    cd portfolio-builder
    ```

2.  **Install dependencies**
    ```bash
    npm install
    ```

3.  **Set up environment variables**
    ```bash
    cp .env.example .env.local
    # Edit .env.local with your configuration
    ```

4.  **Compile theme CSS files**
    ```bash
    npm run sync-themes
    ```

5.  **Start the development server**
    ```bash
    npm run dev
    ```

6.  **Open your browser**
    Navigate to `http://localhost:3000`

---

## 🏗️ Architecture Overview

Portfolio Builder uses a modern **Jamstack architecture** with:

**✅ Core Systems:**
- **Live DOM Capture Export** - Revolutionary export technology
- **Modular Theme System** - Scalable theme architecture
- **Firebase Integration** - Authentication and real-time database
- **Cloudinary Integration** - Optimized image management
- **Default Content System** - Pre-populated sample data for new portfolios

For detailed architecture information, see the **[🏗️ Architecture Overview](docs/ARCHITECTURE.md)**.

## 🎨 Theme Development

Create custom themes with our powerful theme system:

```bash
# Create new theme
node scripts/scaffold-theme.js my-awesome-theme

# Develop and test
npm run sync-themes
npm run dev
```

For complete theme development guide, see **[🎨 Theme Development](docs/THEME_DEVELOPMENT.md)**.

## 📤 Export System

Portfolio Builder features a revolutionary **Live DOM Capture Export System** that creates pixel-perfect static websites. The system:

1. **Captures** the actual rendered DOM from your live portfolio
2. **Cleans** and optimizes the HTML for static hosting
3. **Generates** a complete ZIP package with HTML, CSS, and assets
4. **Ensures** exported sites match live portfolios exactly

For technical details, see **[📤 Export System](docs/EXPORT_SYSTEM.md)**.

## 🤝 Contributing

We welcome contributions! Please see our documentation for:

*   **[🎨 Theme Development](docs/THEME_DEVELOPMENT.md)** - Create custom themes
*   **[🏗️ Architecture Overview](docs/ARCHITECTURE.md)** - Understand the codebase
*   **[🔧 API Reference](docs/API_REFERENCE.md)** - Backend API documentation

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

*   **Next.js Team** - For the amazing framework
*   **Firebase Team** - For reliable backend services
*   **Cloudinary** - For excellent image optimization
*   **Tailwind CSS** - For utility-first styling
*   **Shadcn/UI** - For beautiful component library

## 📞 Support

For questions, issues, or contributions:

1. Check the **[📚 Documentation](docs/)** first
2. Search existing issues on GitHub
3. Create a new issue with detailed information
4. Join our community discussions

---

**Built with ❤️ for developers, designers, and professionals worldwide.**